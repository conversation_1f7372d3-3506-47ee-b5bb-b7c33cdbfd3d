<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\helpers\Arr;
use app\back\components\helpers\Str;
use app\back\components\validators\BaseValidator;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\EmailValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\JsonObjectValidator;
use app\back\components\validators\NestedValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\jsonTypes\EmployeeSettings;

class Employee extends BaseEntity
{
    public const int STATUS_BLOCKED = 0;
    public const int STATUS_ACTIVE = 1;
    public const int STATUS_WAIT = 2;

    public const array STATUSES = [
        self::STATUS_WAIT => 'Confirm wait',
        self::STATUS_BLOCKED => 'Blocked',
        self::STATUS_ACTIVE  => 'Active',
    ];

    public const string PASSWORD_EXPIRATION_PERIOD = '2 years';
    public const string PASSWORD_EXPIRATION_PERIOD_MESSAGE = '2 weeks';

    public const string DEBUG_MODE_COOKIE = 'debug_mode';
    public const string SYSTEM_USER_EMAIL = 'system';

    public const int KYC_DEPARTMENT_ANALYTICS_RISK = 1;
    public const int KYC_DEPARTMENT_FIN_OPERATOR = 2;
    public const int KYC_DEPARTMENT_S2P_RISK = 3;
    public const int KYC_DEPARTMENT_YS_RISK = 4;
    public const int KYC_DEPARTMENT_SMEN_PRODUCT = 5;

    public const array KYC_DEPARTMENTS = [
        self::KYC_DEPARTMENT_ANALYTICS_RISK => 'Analytics risk',
        self::KYC_DEPARTMENT_FIN_OPERATOR => 'Fin operator',
        self::KYC_DEPARTMENT_S2P_RISK => 'S2P risk',
        self::KYC_DEPARTMENT_YS_RISK => 'YS risk',
        self::KYC_DEPARTMENT_SMEN_PRODUCT => 'Smen product',
    ];

    public int $employee_id;
    #[NestedValidator([self::class, 'emailValidator'])]
    public ?string $email;
    #[StringValidator(1, 65)]
    public ?string $password;
    #[StringValidator(32, 32)]
    public ?string $auth_key;
    #[IntInArrayValidator(self::STATUSES)]
    public ?int $status;
    #[StringValidator(32, 32)]
    public ?string $recovery_token;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $recovery_token_expired_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $password_expired_at;
    #[JsonObjectValidator]
    public array $settings = [];
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[BooleanValidator]
    public ?bool $has_feedback;
    #[IntInArrayValidator(self::KYC_DEPARTMENTS)]
    public ?int $kyc_department;

    public static function emailValidator(mixed $value): BaseValidator
    {
        if ($value === self::SYSTEM_USER_EMAIL) {
            return new StringValidator();
        }

        return new EmailValidator();
    }

    public function isPasswordExpiring(): bool
    {
        if ($this->password_expired_at === null) {
            return false;
        }

        return $this->password_expired_at < date('Y-m-d H:i:s', strtotime(static::PASSWORD_EXPIRATION_PERIOD_MESSAGE));
    }

    public function getShortName(): string
    {
        return Str::emailToLdap($this->email);
    }

    public function favoritePages(): array
    {
        return $this->settings['favoritePages'] ?? [];
    }

    /**
     * Generates password hash from password and sets it to the model
     */
    public function setPassword(string $password): void
    {
        $this->password = password_hash($password, PASSWORD_DEFAULT, ['cost' => 13]);
        $this->password_expired_at = new \DateTimeImmutable(self::PASSWORD_EXPIRATION_PERIOD);
    }

    public function generateAuthKey(): void
    {
        $this->auth_key = bin2hex(random_bytes(16));
    }

    public function generatePasswordRecoveryToken(): void
    {
        $this->recovery_token = bin2hex(random_bytes(16));
        $this->recovery_token_expired_at = new \DateTimeImmutable('+1 hour');
    }

    public function getSettingsObject(): EmployeeSettings
    {
        $result = new EmployeeSettings();
        Arr::configure($result, $this->settings, true);

        return $result;
    }
}

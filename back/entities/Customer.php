<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntValidator;

class Customer extends BaseEntity
{
    #[IdValidator]
    public int $id;
    #[IntValidator]
    public ?int $created_by;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[IntValidator]
    public ?int $updated_by;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $updated_at;
}

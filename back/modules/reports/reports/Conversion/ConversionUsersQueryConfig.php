<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Conversion;

use app\back\entities\Rate;
use app\back\entities\UserWallet;
use app\back\modules\reports\columns\AffParamAppTokenColumn;
use app\back\modules\reports\columns\AffParamClickIdColumn;
use app\back\modules\reports\columns\AffDataColumn;
use app\back\modules\reports\columns\AffParamsColumn;
use app\back\modules\reports\columns\AffParamSubIdColumn;
use app\back\modules\reports\columns\BrandColumn;
use app\back\modules\reports\columns\CityColumn;
use app\back\modules\reports\columns\CountryColumn;
use app\back\modules\reports\columns\CurrencyColumn;
use app\back\modules\reports\columns\CurrencyFilterColumn;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\HostColumn;
use app\back\modules\reports\columns\IgnoreColumn;
use app\back\modules\reports\columns\LandingPageColumn;
use app\back\modules\reports\columns\LocationColumn;
use app\back\modules\reports\columns\MarketingTidBannerColumn;
use app\back\modules\reports\columns\MarketingTidBannerIdColumn;
use app\back\modules\reports\columns\MarketingTidDealColumn;
use app\back\modules\reports\columns\MarketingTidPublisherColumn;
use app\back\modules\reports\columns\ModeColumn;
use app\back\modules\reports\columns\RefcodeAppIdColumn;
use app\back\modules\reports\columns\RefcodeColumn;
use app\back\modules\reports\columns\RefcodeProgramIdColumn;
use app\back\modules\reports\columns\RefcodePublisherColumn;
use app\back\modules\reports\columns\SimpleColumn;
use app\back\modules\reports\columns\SiteHostForToxicUserColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\SiteUserColumn;
use app\back\modules\reports\columns\TrafficSourceColumn;
use app\back\modules\reports\columns\UseragentAppColumn;
use app\back\modules\reports\columns\UseragentAppGroupColumn;
use app\back\modules\reports\columns\UseragentBrowserColumn;
use app\back\modules\reports\columns\UseragentBrowserVersionColumn;
use app\back\modules\reports\columns\UseragentColumn;
use app\back\modules\reports\columns\UseragentDeviceColumn;
use app\back\modules\reports\columns\UseragentPlatformColumn;
use app\back\modules\reports\columns\UseragentPlatformGroupColumn;
use app\back\modules\reports\columns\UseragentPlatformVersionColumn;
use app\back\modules\reports\columns\UseragentVariantColumn;
use app\back\modules\reports\columns\UseragentVariantVersionColumn;
use app\back\modules\reports\columns\UserCidColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\columns\UserIsAffHiddenColumn;
use app\back\modules\reports\columns\UserLocaleColumn;
use app\back\modules\reports\columns\UserPriorityTypeOfGamblingColumn;
use app\back\modules\reports\columns\UserRegistrationMethodColumn;
use app\back\modules\reports\columns\UserSocialIdColumn;
use app\back\modules\reports\columns\UserStatusColumn;
use app\back\modules\reports\columns\VipAffAffSourceColumn;
use app\back\modules\reports\columns\VipAffProgTypeColumn;
use app\back\modules\reports\columns\WebmasterAffOwnerColumn;
use app\back\modules\reports\columns\WebmasterAffOwnerGroupColumn;
use app\back\modules\reports\columns\WebmasterIdColumn;
use app\back\modules\reports\columns\WebmasterProgramTypeColumn;
use app\back\modules\reports\components\BaseQueryConfig;
use app\back\repositories\AffData;
use app\back\repositories\AffParams;
use app\back\repositories\Cities;
use app\back\repositories\Hosts;
use app\back\repositories\LandingPages;
use app\back\repositories\MarketingTids;
use app\back\repositories\Refcodes;
use app\back\repositories\UseragentApps;
use app\back\repositories\UseragentPlatforms;
use app\back\repositories\Useragents;
use app\back\repositories\UserLogins;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\UserWallets;
use app\back\repositories\VipaffRefcodes;
use app\back\repositories\WpPrograms;
use app\back\repositories\WpWebmasters;
use Yiisoft\Db\Query\Query;

class ConversionUsersQueryConfig extends BaseQueryConfig
{
    private const array DATE_TABLE_ALIAS_MAP = [
        ConversionConfig::MODE_REG => [
            'column' => 'date',
            'alias' => 'u'
        ],
        ConversionConfig::MODE_FD => [
            'column' => 'dep_first_at',
            'alias' => 'usi'
        ]
    ];

    private string $filterDateColumn = self::DATE_TABLE_ALIAS_MAP[ConversionConfig::MODE_REG]['column'];
    private string $filterTableAlias = self::DATE_TABLE_ALIAS_MAP[ConversionConfig::MODE_REG]['alias'];

    protected function beforeQuery(): void
    {
        $this->request->select('site_id', 'user_id', 'date');

        $mode = $this->request->getFilter('mode');
        $this->filterDateColumn = self::DATE_TABLE_ALIAS_MAP[$mode]['column'];
        $this->filterTableAlias = self::DATE_TABLE_ALIAS_MAP[$mode]['alias'];
    }

    public function selects(): array
    {
        return [
            'site_id' => [SiteIdColumn::class, $this->filterTableAlias],
            'user_id' => [UserIdColumn::class, $this->filterTableAlias],
            'date' => [DateColumn::class, [$this->filterTableAlias => $this->filterDateColumn]],

            'country' => [CountryColumn::class, 'u'],
            'host' => [HostColumn::class, 'ul'],
            'host_toxic' => [SiteHostForToxicUserColumn::class, 'sh'],
            'brand_id' => [BrandColumn::class, 'u'],
            'social_net' => [UserSocialIdColumn::class, 'u'],
            'user_status' => [UserStatusColumn::class, 'u'],
            'confirm' => [SimpleColumn::class, ['expr' => 'COALESCE(u.email_confirm, false) OR COALESCE(u.phone_confirm, false)', 'u']],
            'city' => [CityColumn::class, 'c'],
            'cid' => [UserCidColumn::class, 'u'],
            'locale' => [UserLocaleColumn::class, 'u'],
            'is_aff_hidden' => [UserIsAffHiddenColumn::class, 'u'],
            'user_currency' => [CurrencyColumn::class, 'uw'],
            'priority_type_of_gambling' => [UserPriorityTypeOfGamblingColumn::class, 'u'],
            'registration_method' => [UserRegistrationMethodColumn::class, 'u'],
            'location' => [LocationColumn::class, 'u'],

            'refcode' => [RefcodeColumn::class, 'r'],
            'traffic_source' => [TrafficSourceColumn::class, 'r'],
            'webmaster_id' => [WebmasterIdColumn::class, 'r'],
            'ref_app_id' => [RefcodeAppIdColumn::class, 'r'],
            'ref_program_id' => [RefcodeProgramIdColumn::class, 'r'],
            'publisher_from_refcode' => [RefcodePublisherColumn::class, 'r'],
            'aff_owner' => [WebmasterAffOwnerColumn::class, 'wp_w', 'refcodeTableAlias' => 'r'],
            'aff_owner_group' => [WebmasterAffOwnerGroupColumn::class, 'wp_w', 'refcodeTableAlias' => 'r'],
            'program_type' => [WebmasterProgramTypeColumn::class, 'wp_p'],
            'publisher' => [MarketingTidPublisherColumn::class, 'tids'],
            'deal' => [MarketingTidDealColumn::class, 'tids'],
            'banner' => [MarketingTidBannerColumn::class, 'tids'],
            'banner_id' => [MarketingTidBannerIdColumn::class, 'tids'],
            'aff_data' => [AffDataColumn::class, 'ad'],
            'aff_params' => [AffParamsColumn::class, 'ap'],
            'click_id' => [AffParamClickIdColumn::class, 'ap'],
            'app_token' => [AffParamAppTokenColumn::class, 'ap'],
            'aff_sub_id_1' => [AffParamSubIdColumn::class, 'ap', 'index' => 1],
            'aff_sub_id_2' => [AffParamSubIdColumn::class, 'ap', 'index' => 2],
            'aff_sub_id_3' => [AffParamSubIdColumn::class, 'ap', 'index' => 3],
            'aff_sub_id_4' => [AffParamSubIdColumn::class, 'ap', 'index' => 4],
            'aff_sub_id_5' => [AffParamSubIdColumn::class, 'ap', 'index' => 5],
            'vip_aff_aff_source' => [VipAffAffSourceColumn::class, 'var'],
            'vip_aff_program_type' => [VipAffProgTypeColumn::class, 'var'],
            'lp' => [LandingPageColumn::class, 'lp'],

            'useragent' => [UseragentColumn::class, 'uag'],
            'platform_id' => [UseragentPlatformColumn::class, 'uag'],
            'platform_group' => [UseragentPlatformGroupColumn::class, 'uagp'],
            'browser_id' => [UseragentBrowserColumn::class, 'uag'],
            'variant_id' => [UseragentVariantColumn::class, 'uag'],
            'app_id' => [UseragentAppColumn::class, 'uag'],
            'app_group_id' => [UseragentAppGroupColumn::class, 'uaga'],
            'platform_version' => [UseragentPlatformVersionColumn::class, 'uag'],
            'browser_version' => [UseragentBrowserVersionColumn::class, 'uag'],
            'device_id' => [UseragentDeviceColumn::class, 'uag'],
            'variant_version' => [UseragentVariantVersionColumn::class, 'uag'],
        ];
    }

    public function filters(): array
    {
        return [
            'site_id' => [SiteIdColumn::class, $this->filterTableAlias],
            'user_id' => [UserIdColumn::class, $this->filterTableAlias],
            'site_user' => [SiteUserColumn::class, $this->filterTableAlias],
            'date' => [DateColumn::class, [$this->filterTableAlias => $this->filterDateColumn]],
            'ignore' => [IgnoreColumn::class, $this->filterTableAlias],
            'mode' => [ModeColumn::class],

            'country' => [CountryColumn::class, 'u'],
            'city' => [CityColumn::class, 'c'],
            'social_net' => [UserSocialIdColumn::class, 'u'],
            'user_status' => [UserStatusColumn::class, 'u'],
            'host' => [HostColumn::class, 'ul'],
            'brand_id' => [BrandColumn::class, 'u'],
            'locale' => [UserLocaleColumn::class, 'u'],
            'is_aff_hidden' => [UserIsAffHiddenColumn::class, 'u'],
            'user_currency' => [CurrencyFilterColumn::class, 'uw', 'currencies' => Rate::currencies(), 'allowMultiple' => true],
            'priority_type_of_gambling' => [UserPriorityTypeOfGamblingColumn::class, 'u'],
            'registration_method' => [UserRegistrationMethodColumn::class, 'u'],
            'location' => [LocationColumn::class, 'u'],

            'refcode' => [RefcodeColumn::class, 'r'],
            'traffic_source' => [TrafficSourceColumn::class, 'r'],
            'webmaster_id' => [WebmasterIdColumn::class, 'r'],
            'ref_app_id' => [RefcodeAppIdColumn::class, 'r'],
            'ref_program_id' => [RefcodeProgramIdColumn::class, 'r'],
            'aff_owner' => [WebmasterAffOwnerColumn::class, 'wp_w', 'refcodeTableAlias' => 'r'],
            'aff_owner_group' => [WebmasterAffOwnerGroupColumn::class, 'wp_w', 'refcodeTableAlias' => 'r'],
            'program_type' => [WebmasterProgramTypeColumn::class, 'wp_p'],
            'publisher' => [MarketingTidPublisherColumn::class, 'tids'],
            'vip_aff_aff_source' => [VipAffAffSourceColumn::class, 'var'],
            'vip_aff_program_type' => [VipAffProgTypeColumn::class, 'var'],

            'click_id' => [AffParamClickIdColumn::class, 'ap'],
            'app_token' => [AffParamAppTokenColumn::class, 'ap'],

            'useragent' => [UseragentColumn::class, 'uag'],
            'platform_id' => [UseragentPlatformColumn::class, 'uag'],
            'platform_group' => [UseragentPlatformGroupColumn::class, 'uagp'],
            'browser_id' => [UseragentBrowserColumn::class, 'uag'],
            'variant_id' => [UseragentVariantColumn::class, 'uag'],
            'app_id' => [UseragentAppColumn::class, 'uag'],
            'app_group_id' => [UseragentAppGroupColumn::class, 'uaga'],
        ];
    }

    public function groups(): array
    {
        return [];
    }

    public function tableMap(): array
    {

        return [
            'u' => [Users::TABLE_NAME],
            'usi' => [UserSpecialInfos::TABLE_NAME, 'usi.site_id = u.site_id AND usi.user_id = u.user_id'],
            'r' => [Refcodes::TABLE_NAME, 'r.id = u.refcode_id', ['u']],
            'ul' => [UserLogins::TABLE_NAME, 'ul.site_id = u.site_id AND ul.user_id = u.user_id AND ul.reg', ['u']],
            'sh' => [Hosts::TABLE_NAME, 'ul.host_id = sh.id', ['ul']],
            'tids' => [MarketingTids::TABLE_NAME, MarketingTids::tidsJoinExpression('r', 'tids'), ['r']],
            'wp_w' => [WpWebmasters::TABLE_NAME, WpWebmasters::refcodesJoinCondition('wp_w', 'r'), ['r']],
            'wp_p' => [WpPrograms::TABLE_NAME, 'wp_p.id = ' . Refcodes::programExpression(), ['r']],
            'ad' => [AffData::TABLE_NAME, 'ad.id = u.aff_data_id', ['u']],
            'ap' => [AffParams::paramsAsObjJoinQuery('u'), '', ['u'], 'CROSS JOIN LATERAL'],
            'uag' => [Useragents::TABLE_NAME, 'uag.id = u.useragent_id', ['u']],
            'uaga' => [UseragentApps::TABLE_NAME, 'uaga.id = uag.app_id', ['uag']],
            'uagp' => [UseragentPlatforms::TABLE_NAME, 'uagp.id = uag.platform_id', ['uag']],
            'var' => [VipaffRefcodes::TABLE_NAME, 'var.refcode_id = u.refcode_id', ['u']],
            'lp' => [LandingPages::TABLE_NAME, 'lp.id = u.lp_id', ['u']],
            'c' => [Cities::TABLE_NAME, 'c.id = u.city_id'],
            'uw' => [
                fn() => (new Query($this->db))
                    ->select(['currency' => 'uw.currency'])
                    ->from(['uw' => UserWallets::TABLE_NAME])
                    ->where(['AND',
                        ['uw.type' => UserWallet::TYPE_REAL],
                        UserWallets::getActiveCondition('uw'),
                        'u.site_id = uw.site_id AND u.user_id = uw.user_id'
                    ])
                    ->limit(1),
                'true', ['u'], 'LEFT JOIN LATERAL'
            ],
        ];
    }
}

<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\AdsConversion;

use app\back\components\helpers\DateHelper;
use app\back\components\SiteUserBuilder;
use app\back\entities\UserTransaction;
use app\back\modules\reports\columns\BooleanCheckboxColumn;
use app\back\modules\reports\columns\CountUniqUsersColumn;
use app\back\modules\reports\columns\CurrencyColumn;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\DateInputColumn;
use app\back\modules\reports\columns\DayColumn;
use app\back\modules\reports\columns\ModeColumn;
use app\back\modules\reports\columns\MonthColumn;
use app\back\modules\reports\columns\Operators;
use app\back\modules\reports\columns\RefcodeAppIdColumn;
use app\back\modules\reports\columns\SimpleColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\UsersStatsDSumColumn;
use app\back\modules\reports\columns\UsersStatsFCountColumn;
use app\back\modules\reports\columns\UsersStatsFSumColumn;
use app\back\modules\reports\columns\UsersStatsRCountColumn;
use app\back\modules\reports\columns\UsersStatsRSumColumn;
use app\back\modules\reports\columns\UsersStatsWCountColumn;
use app\back\modules\reports\columns\UsersStatsWSumColumn;
use app\back\modules\reports\columns\WeekColumn;
use app\back\modules\reports\components\BaseQueryConfig;
use app\back\repositories\Refcodes;
use app\back\repositories\Useragents;
use app\back\repositories\Users;
use app\back\repositories\UserTransactions;

class AdsConversionUsersQueryConfig extends BaseQueryConfig
{
    private string $currency;
    private string $appIdExpr;
    private array $userTransactionsJoinCondition;

    protected function beforeQuery(): void
    {
        $this->currency = $this->request->getFilter('currency');
        $this->appIdExpr = Refcodes::appIdExpression();

        $this->request->select('app_id');
        $this->request->group('app_id');

        if (empty($this->request->getFilter('reg_date', Operators::GE))) {
            $fromRegDate = $this->request->getFilter('ads_date', Operators::GE);
            $this->request->addFilter('reg_date', $fromRegDate, Operators::GE);
        }

        if (empty($this->request->getFilter('reg_date', Operators::LE))) {
            $toRegDate = $this->request->getFilter('ads_date', Operators::LE);
            $this->request->addFilter('reg_date', DateHelper::nextDay($toRegDate), Operators::L);
        }

        $payDateType = 'us.' . $this->request->getFilter('pay_date_type');

        $connections = [
            'day'   => "($payDateType)::date = (u.date)::date",
            'week'  => "date_trunc('week', $payDateType)::date = date_trunc('week', u.date)::date",
            'month' => "date_trunc('month', $payDateType)::date = date_trunc('month', u.date)::date",
        ];

        $toActionDate = empty($this->request->getFilter('action_date')) ? $this->request->getFilter('ads_date', Operators::LE) : $this->request->getFilter('action_date');
        $dateGe = $this->request->getFilter('ads_date', Operators::GE);

        $this->userTransactionsJoinCondition = [
            'AND',
            'us.site_id = u.site_id',
            'us.user_id = u.user_id',
            ['>=', $payDateType, $dateGe],
            ['<=', $payDateType, DateHelper::nextDay($toActionDate)],
            ['us.status' => UserTransaction::STATUS_SUCCESS],
            ['us.ext_type' => UserTransaction::EXT_TYPE_NORMAL],
        ];

        if ((bool)$this->request->getFilter('action_join_dates') === true) {
            foreach ($connections as $period => $condition) {
                if (array_key_exists($period, $this->request->groups())) {
                    $this->userTransactionsJoinCondition[] = $condition;
                }
            }
        }
    }

    public function selects(): array
    {
        return [
            'regs' => [CountUniqUsersColumn::class, 'u', 'title' => 'Regs'],
            'fd_count' => [UsersStatsFCountColumn::class, 'us'],
            'rd_count' => [UsersStatsRCountColumn::class, 'us'],
            'wd_count' => [UsersStatsWCountColumn::class, 'us'],
            'fd_revenue' => [UsersStatsFSumColumn::class, 'us', 'currency' => $this->currency],
            'rd_revenue' => [UsersStatsRSumColumn::class, 'us', 'currency' => $this->currency],
            'wd_revenue' => [UsersStatsWSumColumn::class, 'us', 'currency' => $this->currency],
            'd_revenue' => [UsersStatsDSumColumn::class, 'us', 'currency' => $this->currency],
            'app_id' => [RefcodeAppIdColumn::class, 'r'],
            'platform_id' => [SimpleColumn::class, ['expr' => "FIRST_VALUE(uag.platform_id) OVER (PARTITION BY {$this->appIdExpr} ORDER BY COUNT(DISTINCT " . SiteUserBuilder::siteUserQueryExpression('u') . ") DESC)", 'uag']],
            'month' => [MonthColumn::class, ['u' => 'date']],
            'week' => [WeekColumn::class, ['u' => 'date']],
            'day' => [DayColumn::class, ['u' => 'date']],
            'site_id' => [SiteIdColumn::class, 'u'],
        ];
    }

    public function filters(): array
    {
        return [
            'ads_date' => [DateInputColumn::class, 'u'],
            'action_join_dates' => [BooleanCheckboxColumn::class],
            'reg_date' => [DateColumn::class, 'u'],
            'action_date' => [DateInputColumn::class, 'u'],
            'site_id' => [SiteIdColumn::class, 'u'],
            'currency' => [CurrencyColumn::class],
            'pay_date_type' => [ModeColumn::class],
        ];
    }

    public function groups(): array
    {
        return [
            'site_id' => [SiteIdColumn::class, 'u'],
            'month' => [MonthColumn::class, ['u' => 'date']],
            'week' => [WeekColumn::class, ['u' => 'date']],
            'day' => [DayColumn::class, ['u' => 'date']],
            'platform_id' => [SimpleColumn::class, ['uag' => 'platform_id']],
            'app_id' => [RefcodeAppIdColumn::class, 'r'],
        ];
    }

    public function tableMap(): array
    {
        return [
            'u' => [Users::TABLE_NAME],
            'r' => [Refcodes::TABLE_NAME, 'r.id = u.refcode_id'],
            'us' => [UserTransactions::TABLE_NAME, $this->userTransactionsJoinCondition],
            'uag' => [Useragents::TABLE_NAME, 'uag.id = u.useragent_id'],
        ];
    }
}

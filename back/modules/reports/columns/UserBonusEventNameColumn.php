<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\helpers\Arr;
use app\back\components\validators\IntArrayValidator;
use app\back\entities\Bonus;
use Yiisoft\Db\Connection\ConnectionInterface;

class UserBonusEventNameColumn extends BaseColumn implements Selected, Filtered, Decorated
{
    use FilterAndSelectDefault;

    public string $column = 'event_id';
    public string $title = 'Event';

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'list' => Arr::assocToIdName(Bonus::EVENTS),
        ];
    }

    public function rule(): array
    {
        return [IntArrayValidator::class, Bonus::EVENTS];
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $query->andWhere([$this->selectExpression($db, $query) => $value]);
    }

    public function decorate($value, array $row)
    {
        return Bonus::EVENTS[$value] ?? null;
    }
}

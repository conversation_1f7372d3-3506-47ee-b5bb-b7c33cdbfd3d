<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\helpers\Arr;
use app\back\components\validators\StringArrayValidator;
use app\back\entities\enums\VipaffAffSource;
use Yiisoft\Db\Connection\ConnectionInterface;

class VipAffAffSourceColumn extends BaseColumn implements Selected, Filtered, Operators
{
    public string $title = 'VipAff aff source';
    public string $column = 'aff_source';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $this->filterInNotIn($db, $query, $value, $operator);
    }

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'list' => Arr::columnToIdName(VipaffAffSource::values()),
        ];
    }

    public function rule(): array
    {
        return [StringArrayValidator::class, VipaffAffSource::values(), true];
    }

    public function width(): int
    {
        return 6;
    }

    public function operators(): array
    {
        return Operators::IN_NOT_IN;
    }
}

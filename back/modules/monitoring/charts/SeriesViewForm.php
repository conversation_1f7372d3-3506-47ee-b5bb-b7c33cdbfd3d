<?php

declare(strict_types=1);

namespace app\back\modules\monitoring\charts;

use app\back\components\Container;
use app\back\components\Form;
use app\back\modules\monitoring\charts\traits\PanelRowColValidatorTrait;
use app\back\repositories\ChartPanels;

class SeriesViewForm
{
    use Form;
    use PanelRowColValidatorTrait;

    public function __construct(
        private readonly ChartPanels $chartPanelsRepo,
        private readonly Container $container,
    ) {
    }

    public function complexResponse(): array
    {
        $series = $this->panel->chart($this->row, $this->col)->series() ?? [];

        $resultSeries = [];
        foreach ($series as $serie) {
            $source = $serie->getChartSource($this->container);
            $resultSeries[] = [
                'serie' => $serie->response(),
                'sourceConfig' => $serie->sourceConfig,
                'inputsSets' => $source->elementsToInputs($source::CATEGORY_FILTERS),
                'metricDropdown' => $source->elementsToDropdown($source::CATEGORY_METRICS, [
                    'groups' => [],
                    'multiple' => false
                ]),
            ];
        }

        return [
            'series' => $resultSeries,
        ];
    }
}

<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\components\helpers\DateHelper;

trait TaskWithSplitFromToRequest
{
    public string $splitPeriod = 'P1D';

    private function getSplitData(): iterable
    {
        foreach (DateHelper::splitPeriod($this->from, $this->to, $this->splitPeriod, true) as [$from, $to]) {
            yield from $this->createRequest([
                'from' => $from,
                'to' => $to,
            ])->finalData();
        }
    }
}

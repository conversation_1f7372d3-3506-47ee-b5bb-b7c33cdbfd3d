<template>
    <div class="card">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                <li
                    v-for="(tab, tabIndex) in tabs"
                    class="nav-item"
                >
                    <RouterLink
                        v-if="tab.route"
                        exact-active-class="active"
                        :to="tab.route"
                        class="nav-link"
                    >
                        {{ tab.title }}
                        <span
                            v-if="tab.badge"
                            class="badge bg-primary align-middle"
                        >{{ tab.badge }}</span>
                    </RouterLink>
                    <a
                        v-else
                        href="javascript:void(0);"
                        class="nav-link"
                        :class="tabIndex === activeTabIndex ? 'active' : ''"
                        @click.prevent="activateTab(tabIndex)"
                    >{{ tab.title }} <span
                        v-if="tab.badge"
                        class="badge bg-primary align-middle"
                    >{{ tab.badge }}</span></a>
                </li>
                <slot name="afterTabs" />
            </ul>
        </div>

        <div class="card-body">
            <slot :tab="activeTab as T" />
        </div>
    </div>
</template>

<script lang="ts" setup generic="T extends Tab = Tab">
import { computed } from 'vue'
import { Tab } from '@/types'

const $props = withDefaults(defineProps<{
    tabs?: T[]
    activeTabIndex?: number
}>(), {
    tabs: undefined,
    activeTabIndex: 0,
})

const $emit = defineEmits<{
    'update:activeTabIndex': [index: number]
    onActivateTab: [index: number]
}>()

const activeTab = computed(() => {
    const tabs = $props.tabs ?? []
    if ($props.activeTabIndex < tabs.length) {
        return tabs[$props.activeTabIndex]
    }
    return {}
})

function activateTab(index: number) {
    $emit('update:activeTabIndex', index)
    $emit('onActivateTab', index)
}
</script>

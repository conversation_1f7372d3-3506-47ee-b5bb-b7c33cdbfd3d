<template>
    <Card width="wide">
        <WithdrawalsTable
            ref="withdrawals-table"
            @openPopup="onOpenPopup"
            @deny="denyTarget = $event"
        />

        <Popup
            width="full"
            class="px-3"
            :opened="!!openedPopup"
            :title="popupTitle"
            @close="onClosePopup"
        >
            <UserTransactions
                v-if="openedPopup === 'user'"
                v-bind="popupParams"
                @deny="denyTarget = $event"
            />
            <TransactionsUndo
                v-else-if="openedPopup === 'undo'"
                v-bind="popupParams"
            />
        </Popup>
        <DenyPopover :denyTarget="denyTarget" />
    </Card>
</template>

<script lang="ts">
import { Popup } from '@/components'
import { UserTransactions, Card } from '@/widgets'
import WithdrawalsTable from './withdrawals-table.vue'
import TransactionsUndo from './transaction-undo.vue'
import { defineComponent } from 'vue'
import { NextWithReload, WithReloadAndInit } from '@/types'
import DenyPopover, { DenyTargetType } from '@/apps/finance/withdrawals/deny-popover.vue'

export interface PopupParams {
    refreshCallback: () => void
    siteUser?: {
        siteId: number
        userId: number
    }
}

export default defineComponent({
    components: {
        DenyPopover,
        Card,
        Popup,
        WithdrawalsTable,
        TransactionsUndo,
        UserTransactions,
    },

    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => ((vm.$refs['withdrawals-table']) as WithReloadAndInit).reload(vm.$decodeParams(to.query)))
    },

    data () {
        return {
            denyTarget: undefined as undefined | DenyTargetType,
            openedPopup: null as null | string,
            popupParams: {} as PopupParams,
        }
    },

    computed: {
        popupTitle (): string {
            switch (this.openedPopup) {
                case 'user':
                    return `Transactions - ${this.popupParams?.siteUser?.userId}`
                case 'undo':
                    return 'Undo withdrawals'
            }

            return ''
        },
    },

    methods: {
        onOpenPopup ({ name, params }: { name: string, params: PopupParams }) {
            this.popupParams = params
            this.openedPopup = name
        },

        onClosePopup () {
            this.openedPopup = null
        },
    },
})
</script>

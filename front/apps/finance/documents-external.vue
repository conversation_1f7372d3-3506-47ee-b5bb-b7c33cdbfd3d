<template>
    <Card
        width="wide"
        tabindex="0"
        @keyup.space.prevent="onApprove"
        @keyup.esc.prevent="onReset"
    >
        <template #afterHelp="{}">
            <li>
                <button
                    type="button"
                    class="ms-1 btn btn-secondary"
                    title="Load next user"
                    :disabled="inProgress"
                    @click="onReset"
                >
                    <Icona name="icn-refresh" />
                </button>
            </li>
            <li class="col-md-4 col-12">
                <FormGrid
                    v-if="filterForm.blocks"
                    class="filter-from"
                    v-bind="filterForm"
                    :enabled="!inProgress"
                    @change="onFilterChange"
                />
            </li>
        </template>
        <DocumentsAwaitApproveLink
            :await-approve-count="awaitApproveCount"
            :href="EXTERNAL_DOCS_URL + '/external'"
            name="External"
        />
        <span
            v-if="!inProgress || files.length > 0"
            class="me-4"
        >
            <router-link
                v-if="siteUser && siteId && userId"
                :to="{path: '/user/player/' + siteId + '-' + userId}"
            >
                {{ siteUser }}
            </router-link>
            <span
                v-if="!foundUser && siteId && userId"
                class="bg-danger rounded px-2 py-1 ms-2"
            >
                User not found
            </span>
        </span>
        <span
            v-if="currency"
            class="me-4"
        >Currency: <span class="badge bg-cyan mx-1">{{ (currency || '').toUpperCase() }}</span></span>
        <span
            v-if="country"
            class="me-4"
        >Country: <span class="badge bg-purple mx-1">{{ (country || '').toUpperCase() }}</span></span>
        <span
            v-if="locale"
            class="me-4"
        >Locale: <span class="badge bg-orange mx-1">{{ (locale || '').toUpperCase() }}</span></span>

        <div
            v-if="files.length > 0"
            class="mt-5"
        >
            <div
                id="vue-documents-gallery"
                class="row"
                tabindex="0"
            >
                <div
                    v-for="(file, i) in files"
                    :key="`img-block-${i}`"
                    class="col-sm-2"
                >
                    <GalleryImage
                        :key="`img-${i}`"
                        :doc="{src: file.url}"
                        class="img-tile d-block"
                        :class="{'border border-danger border-3': attemptedApprove && file.needApprove && file.tags.length === 0}"
                    >
                        <GalleryImageRotate
                            v-if="file.needApprove"
                            :file="file"
                            @after-rotate="initGallery"
                        />
                        <GalleryImageHeader
                            v-else
                            :text="file.tags.map(tag => tags[tag] || tag).join(', ').concat(...file.tagsExternal || [])"
                        />
                        <GalleryImageFooter
                            :infos="[file.country || [], file.date].flat() as string[]"
                            :siteUser="siteUser || ''"
                            :docId="file.id"
                            :siteId="siteId"
                            :userId="userId"
                        />
                    </GalleryImage>
                    <span
                        v-if="file.needApprove"
                        class="text-center d-inline-block my-4"
                    >
                        <span
                            v-for="(name, tag) in tags"
                            :key="name as string"
                            class="btn btn-sm rounded py-1 px-2 m-1 d-inline-block pointer"
                            :class="file.tags.includes(tag) ? 'btn-primary' : 'btn-secondary'"
                            @click="file.tags = file.tags.includes(tag) ? file.tags.filter(t => t !== tag) : file.tags.filter(t => t !== tagTrash).concat([tag])"
                        >{{ name }}</span>
                        <span
                            v-for="tag in file.tagsExternal"
                            :key="tag"
                            class="btn btn-sm btn-outline-warning rounded py-1 px-2 m-1 d-inline-block disabled"
                        >{{ tag }}</span>
                        <span
                            class="btn btn-sm rounded py-1 px-2 m-1 d-inline-block pointer border-1"
                            :class="file.tags.includes(tagTrash) ? 'btn-danger' : 'btn-outline-danger'"
                            @click="file.tags = [tagTrash]"
                        >{{ tagTrash }}</span>
                    </span>
                </div>
            </div>
        </div>
        <p
            v-else
            class="font-italic text-center"
        >
            <template v-if="inProgress">
                Loading...
            </template>
            <template v-else>
                No documents yet
            </template>
        </p>
        <hr v-if="files.length">
        <FormGrid
            v-if="files.length"
            v-bind="approveForm"
            class="approve-from"
            :enabled="!inProgress"
            @change="onChangeApprove"
            @submit="onApprove"
        />
    </Card>
</template>

<script lang="ts">
import { FormGrid, GalleryImage, GalleryImageHeader, GalleryImageFooter, GalleryImageRotate, Icona } from '@/components'
import { Card } from '@/widgets'
import { GalleryMixin } from '@/utils'
import DocumentsAwaitApproveLink from './documents-await-approve-link.vue'
import { defineComponent } from 'vue'
import { Values, FormGridType, DocFile, NextWithReload } from '@/types'

const EXTERNAL_DOCS_URL = '/finance/documents'

interface SearchForm extends FormGridType {
    siteId: number
    userId: number
    siteUser: string
    foundUser: boolean
    currency: string
    country: string
    locale: string
    awaitApproveCount: number
    tags: Values
    files: DocFile[]
    tagTrash: string
}

export default defineComponent({
    components: {
        Icona,
        GalleryImageRotate,
        Card,
        FormGrid,
        DocumentsAwaitApproveLink,
        GalleryImage,
        GalleryImageHeader,
        GalleryImageFooter,
    },
    mixins: [
        GalleryMixin,
    ],
    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.init(vm.$decodeParams(to.query)))
    },
    beforeRouteUpdate (to, _from, next) {
        const params = this.$decodeParams(to.query)
        if (parseInt(params.siteId as string) !== this.siteId || parseInt(params.userId as string) !== this.userId) {
            this.reload(params)
        }
        next()
    },
    props: {
        site: {
            type: Number,
            default: null,
        },
        user: {
            type: Number,
            default: null,
        },
    },
    data () {
        return {
            EXTERNAL_DOCS_URL,
            siteId: undefined as number | undefined,
            userId: undefined as number | undefined,
            tags: {} as Values,
            tagTrash: 'tagTrash' as string,
            siteUser: undefined as string | undefined,
            awaitApproveCount: 0,
            approveForm: {} as FormGridType,
            filterForm: {} as FormGridType,
            files: [] as DocFile[],
            inProgress: false as boolean,
            foundUser: false as boolean,
            currency: null as string | null,
            country: null as string | null,
            locale: null as string | null,
            emptySiteUserParams: false as boolean,
            attemptedApprove: false as boolean,
        }
    },
    computed: {
        filesToApprove(): DocFile[] {
            return this.files.filter(f => f.needApprove)
        },
        invalidFiles(): DocFile[] {
            return this.filesToApprove.filter(f => f.tags.length === 0)
        },
    },
    methods: {
        init (params: Values) {
            this.emptySiteUserParams = !params.siteId || !params.userId
            this.loadFilterForm({ filterSiteId: params.filterSiteId })
            this.reload(params)
        },
        reload (params: Values) {
            this.loadUser(parseInt(params.siteId as string), parseInt(params.userId as string), params.filterSiteId as number[])
        },
        onChangeApprove (values: Values) {
            // sync country_top with country
            if (this.approveForm.values?.country !== values.country) {
                values.country_top = values.country
            } else if (this.approveForm.values?.country_top !== values.country_top) {
                values.country = values.country_top
            }

            this.approveForm.values = values
        },
        onApprove () {
            if (!this.isUpdateAvailable()) {
                return
            }
            this.attemptedApprove = true;
            if (this.invalidFiles.length) {
                this.$notify({ type: 'error', message: 'Tags required for file(s)' })
                return;
            }
            this.inProgress = true
            this.$fetch(EXTERNAL_DOCS_URL + '/external-approve', {
                files: this.filesToApprove.map(f => f.filename),
                tags: this.filesToApprove.map(f => f.tags),
                siteId: this.siteId,
                userId: this.userId,
                country: this.approveForm.values?.country || null,
            }).then(() => {
                this.inProgress = false
                this.onReset()
            }).catch((res: Response) => {
                this.inProgress = false
                res.json().then((r: {form: FormGridType}) => Object.values((r.form || {}).errors || {}).forEach(error => {
                    this.$notify({ message: error as string, type: 'error' })
                }))
            })
        },
        onReset () {
            if (!this.isUpdateAvailable()) {
                return
            }

            this.reload({ filterSiteId: this.filterForm.values?.filterSiteId })
        },
        isUpdateAvailable () {
            return Boolean(!this.inProgress && document.querySelectorAll('.lum-lightbox.lum-open').length === 0)
        },
        loadUser (siteId: number, userId: number, filterSiteId: number[]) {
            this.filterForm.values = { filterSiteId }
            delete this.siteUser
            this.inProgress = true
            this.currency = null
            this.country = null
            this.locale = null
            this.files = []
            this.$fetch(EXTERNAL_DOCS_URL + '/external-search', {
                siteId,
                userId,
                filterSiteId,
            }).then((form: SearchForm) => {
                this.siteId = form.siteId
                this.userId = form.userId
                this.siteUser = form.siteUser
                this.foundUser = form.foundUser
                this.currency = form.currency
                this.country = form.country
                this.locale = form.locale
                this.files = form.files.map((f: DocFile) => ({
                    ...f,
                    siteId: form.siteId,
                    userId: form.userId,
                }))
                this.awaitApproveCount = form.awaitApproveCount
                this.tags = form.tags
                this.tagTrash = form.tagTrash
                this.approveForm.blocks = form.blocks
                this.approveForm.values = this.approveForm.values || form.values
                const query = filterSiteId ? { filterSiteId, siteId: form.siteId, userId: form.userId } : { siteId: form.siteId, userId: form.userId }
                // replace history with "/external" loading page
                if (this.emptySiteUserParams) {
                    this.emptySiteUserParams = false
                    this.$historyReplaceParams(query)
                } else {
                    this.$historyPushParams(query)
                }
            }).finally(() => {
                this.inProgress = false
                this.$el.focus()
                this.initGallery()
            })
        },
        async loadFilterForm (values: Values) {
            this.filterForm = await this.$fetch(EXTERNAL_DOCS_URL + '/external-filter', {})
            if (values.filterSiteId) {
                await this.$processFormResponse(this.$fetch(EXTERNAL_DOCS_URL + '/external-filter', values), this.filterForm)
            }
        },
        onFilterChange (values: Values) {
            this.emptySiteUserParams = true
            this.filterForm.errors = {}
            this.filterForm.values = values
            if (this.approveForm.values) {
                this.approveForm.values.filterSiteId = this.filterForm.values.filterSiteId
            }
            this.$historyPushParams({ filterSiteId: values.filterSiteId })
        },
    },
})
</script>

<style lang="scss">
    .approve-from label {
        z-index: 0 !important;
    }
    .img-tile img {
        width: 100%;
        height: 300px;
        object-fit: cover;
    }
    .filter-from {
        .label-container {
            display: none;
        }
        .form-group {
            margin-bottom: 0;
        }
    }
</style>

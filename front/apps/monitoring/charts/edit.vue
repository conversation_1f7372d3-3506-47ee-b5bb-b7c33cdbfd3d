<template>
    <div v-if="panel">
        <div class="row">
            <div class="col-sm-10">
                <FormGrid
                    v-bind="panel.form"
                    @change="onPanelChange"
                />
            </div>
            <div class="col-sm-2">
                <label>&nbsp;</label><br>
                <button
                    type="button"
                    class="btn btn-danger"
                    @click="onPanelDelete"
                >
                    <Icona name="icn-delete" /> Delete
                </button>
            </div>
        </div>

        <table class="charts-panel table-fixed">
            <tr v-for="(cols, row) in panel.layout" :key="row">
                <td
                    v-for="(chart, col) in cols"
                    :key="col"
                    class="align-top"
                    :rowspan="Math.max(chart.row_span, 1)"
                    :colspan="Math.max(chart.col_span, 1)"
                >
                    <div
                        class="btn btn-lg d-block"
                        :draggable="!draggedChart.inProcess"
                        :class="getChartSelectorClasses(row, col, chart)"
                        @click.prevent="onChartSelect(String(row), String(col))"
                        @dragstart="onDragStart($event, row, col, chart)"
                        @dragover.prevent
                        @drop.prevent="onDrop($event, row, col, chart)"
                        @dragend.prevent="onDragEnd"
                    >
                        {{ chart.name || '(empty)' }}
                    </div>
                </td>
            </tr>
        </table>
        <div v-if="selectedChart">
            <template v-if="panel.layout[selectedChart.row][selectedChart.col].name === null">
                <FormGrid
                    v-bind="chartAddForm"
                    @change="chartAddForm.values = $event"
                    @submit="onChartAdd"
                />
            </template>
            <template v-else>
                <div class="card mb-4">
                    <div class="card-body">
                        <h3>
                            Chart
                            <button
                                type="button"
                                class="btn btn-danger btn-sm"
                                @click="onChartDelete"
                            >
                                <Icona name="icn-delete" /> Delete
                            </button>
                        </h3>
                        <FormGrid
                            v-bind="chartForm"
                            @change="onChartChange"
                        />
                        <h3>Series</h3>
                        <Tabs :tabs="tabs">
                            <template #default="{ tab }: { tab: SerieTabOrNewSerieTab }">
                                <template v-if="'isAddSerie' in tab">
                                    <FormGrid
                                        v-bind="addSerieForm"
                                        @change="addSerieForm.values = $event"
                                        @submit="onChartNewSerieAdd"
                                    />
                                </template>
                                <template v-else-if="tab.serieForm">
                                    <FormGrid
                                        v-bind="tab.serieForm"
                                        @change="onChartSerieChange"
                                    />
                                    <div class="card-body row px-0">
                                        <div class="col-md-6">
                                            <FormParams
                                                :errors="tab.errors"
                                                :disabled="!!inProcess"
                                                :formValues="tab.flatFormValues!"
                                                :inputsSets="tab.inputsSets!"
                                                @paramClick="onParamClick"
                                            />
                                        </div>
                                        <div class="col-md-6">
                                            <FormList
                                                ref="list"
                                                class="mt-1"
                                                :disabled="!!inProcess"
                                                :focusOnFormValue="focusOnFilter"
                                                :formBlocks="[tab.formValuesBlock!]"
                                                :inputsByName="tab.inputsByName!"
                                                @change="onChange"
                                                @changeOperator="onChangeOperator"
                                                @delete="onDelete"
                                            />
                                        </div>
                                    </div>
                                    <div class="input-group">
                                        <label class="input-group-text">Metric</label>
                                        <Dropdown
                                            :enabled="!inProcess"
                                            :isInvalid="'metric' in (tab.errors || {})"
                                            :value="tab.metric"
                                            :allowToggleAll="false"
                                            v-bind="tab.metricDropdown"
                                            @input="onMetricChanged($event as string | null)"
                                        />
                                        <button
                                            type="button"
                                            class="btn btn-danger"
                                            :disabled="inProcess"
                                            @click="onChartSerieDelete()"
                                        >
                                            <Icona name="icn-delete" /> Delete serie
                                        </button>
                                        <button
                                            v-if="!!panel.showSqlButton && !hasError"
                                            type="button"
                                            class="btn btn-outline-warning"
                                            :disabled="inProcess"
                                            @click="processSqlView(sql === false)"
                                        >
                                            <Icona name="icn-database" /> SQL
                                        </button>
                                        <button
                                            type="button"
                                            class="btn btn-success"
                                            :disabled="inProcess"
                                            @click="onChartSerieClone()"
                                        >
                                            <Icona name="icn-copy" /> Clone serie
                                        </button>
                                    </div>
                                </template>
                            </template>
                        </Tabs>
                    </div>
                </div>

                <div class="card">
                    <div class="card-body">
                        <template v-if="sql && !hasError">
                            <h3>SQL</h3>
                            <pre
                                class="p-3"
                                style="white-space: pre-line;"
                            >{{ sql }}</pre>
                        </template>
                        <h3>Preview</h3>
                        <div
                            id="panels-chart-highchart-preview"
                            style="min-height: 500px;"
                        />
                    </div>
                </div>
            </template>
        </div>
        <p
            v-if="!selectedChart"
            class="font-italic text-center small"
        >
            Select chart to edit or drag to rearrange
        </p>
    </div>
</template>

<script lang="ts">

import { FormList, FormParams, FormGrid, HighchartsLoader, Tabs, Dropdown, Icona } from '@/components'
import { defineComponent } from 'vue'
import { $emptyFormValuesBlock, $emptyListParamWithKey, $fromValuesToUrlParams, $toggleFormValue } from '@/utils/form-list-utils'
import { useTitleBreadcrumbs } from '@/utils/title-breadcrumbs.ts'
import { FormGridType, FormBlock, FormInput, FormInputsSet, FormValue, FormValueIndex, Item, Values, Value, Errors, Tab, NextWithReload, ItemGroup } from '@/types'

interface RouteParams {
    panelId?: string
    rowCol?: string
    serieIndex?: string
}

interface Chart {
    name: string | null
    row_span: number
    col_span: number
}

interface Panel {
    id: number
    form: FormGridType<{
        name: string
        cols: number
        rows: number
    }>
    layout: Chart[][]
    showSqlButton: boolean
}

interface SelectedChart {
    row: number
    col: number
}

interface DraggedChart {
    chart?: Chart
    row?: string
    col?: string
    run?: boolean
    inProcess?: boolean
}

interface Serie {
    name: string
}

interface MetricDropdown {
    items: Item<string>[]
    groups: ItemGroup[]
    multiple: boolean
}

interface SerieSourceConfig {
    metric: string
    filters: [string, Value, string][]
}

interface SerieTab extends Tab {
    serieForm?: FormGridType<Serie>
    metricDropdown?: MetricDropdown
    inputsSets?: FormInputsSet[]
    inputsByName?: Record<string, FormInput>
    metric?: string
    flatFormValues?: FormValue[]
    formValuesBlock?: FormBlock
    errors?: Errors
}

type SerieTabOrNewSerieTab = SerieTab | (Tab & { isAddSerie: true })

type ChartSeriesResponse = {
    series: {
        serie: FormGridType<Serie>
        sourceConfig: SerieSourceConfig
        inputsSets: FormInputsSet[]
        metricDropdown: MetricDropdown
    }[]
}

export default defineComponent({
    components: {
        Icona,
        FormList,
        FormParams,
        Tabs,
        FormGrid,
        Dropdown,
    },
    beforeRouteEnter (to, _from, next: NextWithReload) {
        next((vm) => {
            // Reset state
            vm.init(to.params)
        })
    },
    props: {
        panelId: {
            type: [String],
            required: true,
        },
        rowCol: {
            type: [String],
            default: undefined,
        },
        serieIndex: {
            type: [String],
            default: undefined,
        },
    },
    setup() {
        return {
            titleBreadcrumbs: useTitleBreadcrumbs(),
        }
    },
    data () {
        return {
            sql: false,
            hasError: false,
            inProcess: false,
            panel: undefined as Panel | undefined,
            selectedChart: undefined as SelectedChart | undefined,
            chartAddForm: {} as FormGridType,
            chartForm: {} as FormGridType,
            addSerieForm: {} as FormGridType,
            tabs: [] as SerieTabOrNewSerieTab[],
            activeTab: 0,
            draggedChart: {} as DraggedChart,
            focusOnFilter: undefined as FormValue | undefined,
        }
    },

    computed: {
        selectedPanelIdRowCol () {
            if (!this.selectedChart) {
                return undefined
            }

            return {
                panelId: this.panelId,
                col: this.selectedChart.col,
                row: this.selectedChart.row,
            }
        },
    },

    watch: {
        async rowCol (rowCol: string) {
            const [row, col] = rowCol.split('-').map((v) => parseInt(v))
            this.selectedChart = { row, col }
            await this.chartLoad()
        },
    },

    mounted () {
        this.resetTabs()
    },

    methods: {
        async init (params: RouteParams) {
            this.destroyHighchart()
            this.panel = undefined
            this.selectedChart = undefined
            await this.loadPanel(params.panelId!)
            // TODO: handle rowCol and serieIndex
        },
        async loadPanel (id: string): Promise<void> {
            this.panel = await this.$fetch<Panel>('/monitoring/charts/panel-edit', { panelId: id })
            this.titleBreadcrumbs.setTitle('Edit panel: ' + this.panel?.form.values?.name)
        },
        getChartSelectorClasses (row: number, col: number, chart: Chart): Record<string, boolean> {
            let btnColor = 'btn-secondary'
            if (this.draggedChart.chart && this.draggedChart.chart !== chart) {
                btnColor = this.isChartSpanEqual(this.draggedChart.chart, chart) ? 'btn-success' : 'btn-danger'
            } else if (row === this.selectedChart?.row && col === this.selectedChart?.col) {
                btnColor = 'btn-primary'
            }

            return {
                [btnColor]: true,
                'font-italic': chart.name === null,
            }
        },
        isChartSpanEqual (sChart: Chart, tChart: Chart): boolean {
            return parseInt(String(sChart.row_span)) === parseInt(String(tChart.row_span)) && parseInt(String(sChart.col_span)) === parseInt(String(tChart.col_span))
        },
        async onPanelDelete () {
            if (!confirm('Really delete panel?')) {
                return
            }

            if (!this.panel) {
                return
            }

            return this.$fetch('/monitoring/charts/panel-delete', { panelId: this.panel.id }).then(() => {
                this.$notify({
                    type: 'success',
                    message: 'Panel deleted',
                })
                this.$router.push({ name: 'panel-list' })
            })
        },
        async onPanelChange (params: Values) {
            if (!this.panel || !this.panelId) {
                return
            }
            params.panelId = this.panelId
            await this.$processFormResponse(this.$fetch('/monitoring/charts/panel-save', params), this.panel.form)
            await this.loadPanel(this.panelId)
        },
        onChartSelect (row: string, col: string) {
            this.$historyReplaceParams({ row, col })
        },
        async chartLoad() {
            if (!this.panel || !this.selectedChart) {
                return
            }

            const { row, col } = this.selectedChart
            if (this.panel.layout[row][col].name === null) {
                // New chart form
                this.$fetch('/monitoring/charts/chart-add-form', this.selectedPanelIdRowCol).then((form: FormGridType) => {
                    this.chartAddForm = form
                })
            } else {
                // Existing chart form
                return Promise.all([
                    this.loadChartForm(),
                    this.loadChart(),
                    this.$fetch('/monitoring/charts/serie-add-form', this.selectedPanelIdRowCol).then((form: FormGridType) => {
                        this.addSerieForm = form
                    }),
                ])
            }
        },
        async onChartAdd (params: Values) {
            await this.$processFormResponse(this.$fetch('/monitoring/charts/chart-add', params), this.chartAddForm)
            this.loadPanel(this.panelId).then(() => {
                this.onChartSelect(params.row as string, params.col as string) // TODO: check of row and col is strings (not numbers)
            })
        },
        async onChartDelete () {
            if (!this.panel || !this.selectedChart) {
                return
            }

            if (!confirm('Really delete chart?')) {
                return
            }

            await this.$fetch('/monitoring/charts/chart-delete', this.selectedPanelIdRowCol)
            this.selectedChart = undefined
            await this.loadPanel(this.panelId)
        },
        flatFormFiltersToBlock (formFilters: FormValue[], inputsSets: FormInputsSet[]): FormBlock {
            const inputsNamesToSetIndex: Record<string, number> = {}
            inputsSets.forEach((set, setIndex) => set.inputs.forEach(input => {
                inputsNamesToSetIndex[input.name] = setIndex
            }))

            const block = $emptyFormValuesBlock(inputsSets)
            formFilters.forEach(f => {
                block.blockSets[inputsNamesToSetIndex[f.name]].values.push(f)
            })
            return block
        },
        inputsByName (inputsSets: FormInputsSet[]): Record<string, FormInput> {
            const inputs: Record<string, FormInput> = {}
            inputsSets.forEach(set => set.inputs.forEach(input => {
                inputs[input.name] = input
            }))
            return inputs
        },
        async loadChartForm () {
            if (!this.selectedPanelIdRowCol) {
                return
            }

            try {
                this.chartForm = await this.$fetch('/monitoring/charts/chart-edit', this.selectedPanelIdRowCol)
            } catch (e) {
                this.$notify({
                    type: 'error',
                    message: 'Chart form loading failed! Danger! Can lead to series config loss! Do not edit',
                })
                return
            }

            try {
                const r = await this.$fetch<ChartSeriesResponse>('/monitoring/charts/chart-series', this.selectedPanelIdRowCol)
                this.resetTabs()
                const { panelId, row, col } = this.selectedPanelIdRowCol
                r.series.reverse().forEach(serie => {
                    const flatFormValues = serie.sourceConfig.filters.map(([name, value, operator]: [string, Value, string]) => ({
                        ...$emptyListParamWithKey(),
                        name,
                        value,
                        operator,
                    }))
                    this.tabs.unshift({
                        route: {
                            name: 'chart-edit',
                            params: {
                                panelId: String(panelId),
                                rowCol: row + '-' + col,
                                serieIndex: String(this.tabs.length),
                            },
                        },
                        title: serie.serie.values?.name || 'No name serie',
                        serieForm: serie.serie,
                        metricDropdown: serie.metricDropdown,
                        inputsSets: serie.inputsSets,
                        inputsByName: this.inputsByName(serie.inputsSets),
                        metric: serie.sourceConfig.metric,
                        flatFormValues,
                        formValuesBlock: this.flatFormFiltersToBlock(flatFormValues, serie.inputsSets),
                    })
                })
            } catch (e) {
                this.$notify({
                    type: 'error',
                    message: 'Chart series loading failed! Danger! Can lead to series config loss! Do not edit',
                })
            }
        },
        onChartChange (chart: Values): void {
            interface ChartConfig extends Values {
                series?: unknown
            }
            const chartConfig = chart as ChartConfig
            const params = Object.assign({ config: chartConfig }, this.selectedPanelIdRowCol)
            delete params.config.series
            this.$processFormResponse(this.$fetch('/monitoring/charts/chart-save', params), this.chartForm as FormGridType).then(() => {
                this.loadPanel(this.panel.id!)
                this.loadChartForm()
            }).catch(() => this.hasError = true)
        },
        processSqlView (show: boolean): Promise<string> | void {
            if (!show) {
                this.sql = false
                return
            }
            interface ChartConfig {
                xAxis: Array<{ range: number }>
            }
            return show && this.$fetch<ChartConfig>('/monitoring/charts/chart-config', this.selectedPanelIdRowCol).then((conf: ChartConfig) => {
                const to = (new Date()).valueOf() - 60 * 1000
                const from = (new Date(to - conf.xAxis[0].range)).valueOf()
                return this.loadSql(from, to)
            })
        },
        loadSql (from: number, to: number): Promise<void> {
            this.sql = false
            interface SqlResponse {
                sql: string
            }
            return this.$fetch<SqlResponse>('/monitoring/charts/chart-serie-sql', Object.assign({}, this.selectedPanelIdRowCol, this.selectedChartSerieId(), { from, to }))
                .then(data => this.sql = data.sql)
        },
        loadChart (): Promise<void> {
            interface HighchartsModule {
                default: {
                    stockChart: (containerId: string, config: unknown) => {
                        series: Array<{ setData: (data: unknown, redraw: boolean) => void }>
                        redraw: () => void
                        destroy: () => void
                    }
                }
            }
            interface ChartConfig {
                xAxis: Array<{ range: number }>
            }
            return this.$fetch<ChartConfig>('/monitoring/charts/chart-config', this.selectedPanelIdRowCol).then((conf: ChartConfig) => {
                this.destroyHighchart()
                return HighchartsLoader().then((module: HighchartsModule) => {
                    const Highcharts = module.default
                    this.$options.highchart = Highcharts.stockChart('panels-chart-highchart-preview', conf)
                    return this.loadChartData(conf)
                })
            }).then((series: unknown[]) => {
                series.forEach((serieData: unknown, i: number) => {
                    this.$options.highchart.series[i].setData(serieData, false)
                })
                this.$options.highchart.redraw()
            }).then(() => {
                this.hasError = false
            })
        },
        loadChartData (conf: { xAxis: Array<{ range: number }> }): Promise<unknown[]> {
            const to = (new Date()).valueOf() - 60 * 1000
            const from = (new Date(to - conf.xAxis[0].range)).valueOf()
            if (this.sql !== false) {
                this.loadSql(from, to)
            }
            return this.$fetch<unknown[]>('/monitoring/charts/chart-data', Object.assign({}, this.selectedPanelIdRowCol, { from, to }))
        },
        resetTabs (): void {
            this.activeTab = 0
            this.tabs = [
                {
                    title: 'Add new serie',
                    isAddSerie: true,
                },
            ]
        },
        destroyHighchart (): void {
            if (this.$options.highchart) {
                this.$options.highchart.destroy()
            }
        },
        selectedChartSerieId () {
            return Object.assign(this.selectedPanelIdRowCol, { serieIndex: this.activeTab } )
        },
        onChartNewSerieAdd (source: Values): void {
            const params = Object.assign(source, this.selectedPanelIdRowCol)
            this.$processFormResponse(this.$fetch('/monitoring/charts/serie-add', params), this.addSerieForm as FormGridType).then(() => {
                this.loadChartForm().then(() => {
                    this.activeTab = Math.max(0, this.tabs.length - 2)
                    this.loadChart()
                })
            })
        },
        onChartSerieDelete (): void {
            if (!confirm('Really delete serie?')) {
                return
            }

            this.$processFormResponse(this.$fetch('/monitoring/charts/serie-delete', this.selectedChartSerieId()), {} as FormGridType /* fake form */).then(() => {
                this.loadChartForm().then(() => {
                    this.loadChart()
                })
            })
        },
        onChartSerieClone (): void {
            this.$processFormResponse(this.$fetch('/monitoring/charts/serie-clone', this.selectedChartSerieId()), {} as FormGridType /* fake form */).then(() => {
                this.loadChartForm().then(() => {
                    this.loadChart()
                })
            })
        },
        onChartSerieChange (serie: Serie): void {
            const params = Object.assign({ config: serie }, this.selectedChartSerieId())
            delete params.config.sourceConfig

            this.$processFormResponse(this.$fetch('/monitoring/charts/serie-save', params), this.tabs[this.activeTab].serieForm!).then(() => {
                this.loadChartForm().then(() => {
                    this.loadChart()
                })
            })
        },
        onParamClick (input: FormInput): void {
            const setIndex = this.tabs[this.activeTab].inputsSets!.findIndex(s => s.inputs.some(f => f.name === input.name))
            this.focusOnFilter = $toggleFormValue(input, this.tabs[this.activeTab].formValuesBlock!.blockSets[setIndex])
        },
        onChange ({ valueIndex, value }: { valueIndex: FormValueIndex, value: Value }): void {
            this.tabs[this.activeTab].formValuesBlock!.blockSets[valueIndex.setIndex].values[valueIndex.valueIndex].value = value
            this.onValuesChanged()
        },
        onChangeOperator ({ valueIndex, operator }: { valueIndex: FormValueIndex, operator: string }): void {
            this.tabs[this.activeTab].formValuesBlock!.blockSets[valueIndex.setIndex].values[valueIndex.valueIndex].operator = operator
            this.onValuesChanged()
        },
        onDelete (valueIndex: FormValueIndex): void {
            this.tabs[this.activeTab].formValuesBlock!.blockSets[valueIndex.setIndex].values.splice(valueIndex.valueIndex, 1)
            this.onValuesChanged()
        },
        onMetricChanged (value: string | null): void {
            this.tabs[this.activeTab].metric = value
            this.onValuesChanged()
        },
        onValuesChanged (): void {
            const curTab = this.tabs[this.activeTab]
            curTab.flatFormValues = curTab.formValuesBlock!.blockSets.map(s => s.values).flat()
            const params = {
                ...this.selectedChartSerieId(),
                chartConfig: { ...$fromValuesToUrlParams(curTab.flatFormValues), ...{ metric: curTab.metric } },
            }

            interface ErrorResponse {
                status: number
                json(): Promise<{ errors?: Errors }>
            }

            this.inProcess = true
            this.$fetch('/monitoring/charts/source-save', params).then(() => {
                delete curTab.errors
                curTab.flatFormValues!.forEach(param => delete param.error)
                return this.loadChart()
            })
                .catch((resp: ErrorResponse) => {
                    this.hasError = true
                    if (resp.status !== 422) {
                        return
                    }

                    return resp.json().then(data => {
                        if (!('errors' in data)) {
                            return
                        }

                        curTab.errors = data.errors
                        curTab.flatFormValues!.forEach((param, index) => {
                            const key = `f${index}e`

                            if (data.errors && key in data.errors) {
                                param.error = data.errors[key]
                            } else if (data.errors && param.name in data.errors) {
                                param.error = data.errors[param.name]
                            } else {
                                delete param.error
                            }
                        })
                    })
                })
                .then(() => this.inProcess = false)
        },
        onDragStart (e: DragEvent, row: number, col: number, chart: Chart): void {
            this.draggedChart = { chart, row, col, run: true }
            // force some browsers enable drag'n'drop
            e.dataTransfer!.setData('text/plain', '')
            e.dataTransfer!.dropEffect = 'move'
        },
        onDragEnd (): void {
            this.draggedChart = {}
        },
        onDrop (e: DragEvent, row: number, col: number, chart: Chart): void {
            if (!this.draggedChart.run || chart === this.draggedChart.chart) {
                return
            }
            if (!this.isChartSpanEqual(chart, this.draggedChart.chart!)) {
                this.$notify({ type: 'warn', message: 'Charts with different sizes cannot be switched' })
                return
            }
            this.draggedChart.inProcess = true
            const { row: draggedRow, col: draggedCol } = this.draggedChart

            let selectedChart = this.selectedChart
            if (parseInt(String(row)) === parseInt(String(this.selectedChart.row)) && parseInt(String(col)) === parseInt(String(this.selectedChart.col))) {
                selectedChart = { row: draggedRow!, col: draggedCol! }
            } else if (parseInt(String(draggedRow)) === parseInt(String(this.selectedChart.row)) && parseInt(String(draggedCol)) === parseInt(String(this.selectedChart.col))) {
                selectedChart = { row, col }
            }
            this.$fetch('/monitoring/charts/chart-switch', {
                sourceRow: draggedRow,
                sourceCol: draggedCol,
                targetRow: row,
                targetCol: col,
                panelId: this.panelId,
            })
                .then(() => this.loadPanel(this.panel.id!)
                    .then(() => this.selectedChart = selectedChart),
                )
                .finally(() => this.draggedChart.inProcess = false)
        },
    },
})
</script>

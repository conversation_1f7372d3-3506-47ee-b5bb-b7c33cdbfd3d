export default [
    {
        path: '/monitoring/charts',
        component: () => import('./charts.vue'),
        meta: {
            title: 'Charts',
        },
        children: [
            {
                path: '',
                name: 'panel-list',
                meta: {
                    title: 'List',
                },
                component: () => import('./charts/list.vue'),
            },
            {
                path: 'edit/:panelId/:rowCol?/:serieIndex?',
                name: 'panel-edit',
                meta: {
                    navigateToPanel: true,
                },
                component: () => import('./charts/edit.vue'),
                props: true,
            },
            {
                path: 'panel/:panelId',
                name: 'panel',
                component: () => import('./charts/panel.vue'),
                props: true,
            },
        ],
    },
]
